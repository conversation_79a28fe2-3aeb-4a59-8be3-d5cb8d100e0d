broot=${PWD}
source=${broot}/source
targets=arm-none-eabi,arm-none-linux-gnueabi,i686-pc-mingw32
staging=${broot}/staging
pyversion=2.7
pydir=${broot}/python
expatversion=2.1.1
iconvversion=1.14
version=7.12
targetdir=${broot}/qtcreator-gdb-${version}
packageparts=${targetdir}/lib ${targetdir}/data-directory ${targetdir}/libiconv-2.dll ${targetdir}/python27.dll ${targetdir}/libexpat-1.dll
arch=`uname -sm | sed 's/ /-/g' | tr A-Z a-z`
packagename=qtcreator-gdb-${version}-${arch}.tar.gz

all: package

clean:
	rm -rf  ${broot}/qtcreator-gdb-* ${staging}/gdb-*

distclean:
	rm -rf ${staging} ${source} ${broot}/qtcreator-gdb-*

makesourcedir:
	@test -e ${source} || mkdir ${source}

maketargetdir:
	@test -e ${targetdir} || mkdir ${targetdir}

makestagingdir:
	@test -e ${staging} || mkdir ${staging}

checkunzip:
	unzip -v &> /dev/null || mingw-get install msys-unzip

${source}/python.zip: | makesourcedir checkwget
	cd ${source} && \
	echo "Downloading python..." && \
	wget -q http://download.qt.io/development_releases/prebuilt/gdb/build-prerequisites/python.zip && \
	touch python.zip

${broot}/python/python.exe: ${source}/python.zip | checkunzip
	cd ${broot} && \
	echo "Extracting python..." && \
	unzip -oq ${source}/python.zip && \
	touch python/python.exe

checkwget:
	wget -V &> /dev/null || mingw-get install msys-wget-bin

${source}/gdb-${version}.tar.xz: | makesourcedir checkwget
	cd ${source} && \
	echo "Downloading gdb..." && \
	wget -q http://ftp.gnu.org/gnu/gdb/gdb-${version}.tar.xz || \
	wget -Oq gdb-${version}.tar.xz http://ftp.gnu.org/gnu/gdb/gdb-${version}a.tar.xz && \
	touch gdb-${version}.tar.xz

${source}/libiconv-${iconvversion}.tar.gz: | makesourcedir checkwget
	cd ${source} && \
	echo "Downloading iconv..." && \
	wget -q http://ftp.gnu.org/pub/gnu/libiconv/libiconv-${iconvversion}.tar.gz  && \
	touch ${source}/libiconv-${iconvversion}.tar.gz

${source}/expat-${expatversion}.tar.bz2: | makesourcedir checkwget
	cd ${source} && \
	echo "Downloading expat..." && \
	wget -q http://sourceforge.net/projects/expat/files/expat/${expatversion}/expat-${expatversion}.tar.bz2/download && \
	touch ${source}/expat-${expatversion}.tar.bz2

${staging}/gdb-${version}/configure: ${source}/gdb-${version}.tar.xz | makestagingdir
	cd ${staging} && \
	echo "Extracting gdb..." && \
	tar xf ${source}/gdb-${version}.tar.xz && \
	cd gdb-${version} && \
	touch configure && \
	patch -p1 < ${broot}/patches/mingw-python.patch

${staging}/lib/libiconv.a: ${source}/libiconv-${iconvversion}.tar.gz | makestagingdir
	cd ${staging} && \
	echo "Extracting iconv..." && \
	tar xf ${source}/libiconv-${iconvversion}.tar.gz && \
	cd libiconv-${iconvversion} && \
	./configure -prefix=${staging} --enable-static --build=i686-pc-mingw32 && \
	${MAKE} && ${MAKE} install

${staging}/lib/libexpat.a: ${source}/expat-${expatversion}.tar.bz2 | makestagingdir
	cd ${staging} && \
	echo "Extracting expat..." && \
	tar xf ${source}/expat-${expatversion}.tar.bz2 && \
	cd expat-${expatversion} && \
	./configure -prefix=${staging} --enable-static --build=i686-pc-mingw32 && \
	${MAKE} && ${MAKE} install

${targetdir}/gdb.exe: ${staging}/lib/libexpat.a ${staging}/lib/libiconv.a ${staging}/gdb-${version}/configure | maketargetdir ${broot}/python/python.exe
	test -e ${staging}/gdb-${version}-build || mkdir ${staging}/gdb-${version}-build
	export PYTHONHOME=${pydir} && \
	LDFLAGS="L${pydir} -lpthread -ldl -lutil -lpython27" && \
	CFLAGS="-I${pydir}/include" && \
	cd ${staging}/gdb-${version}-build && \
	${staging}/gdb-${version}/configure --enable-targets=${targets} --disable-nls --disable-werror --build=i686-pc-mingw32 \
	--with-libiconv-prefix=${staging} \
	--with-expat --with-libexpat-prefix=${staging} \
	--with-separate-debug-dir="" \
	--with-python=${pydir} && \
	${MAKE} MAKEFLAGS+= -j1 && \
	strip -o ${targetdir}/gdb-$*.exe gdb/gdb.exe

package: ${targetdir}/gdb.exe ${broot}/python/python.exe
	cp ${staging}/bin/libiconv* ${targetdir} && \
	cp ${staging}/bin/libexpat* ${targetdir} && \
	cp ${pydir}/python*.dll ${targetdir} && \
	cp -r ${pydir}/lib ${targetdir} && \
	mkdir -p ${targetdir}/data-directory && \
	cp -r ${staging}/gdb-${version}-build/gdb/data-directory/{python,syscalls} ${targetdir}/data-directory && \
	mv ${targetdir} ${targetdir}-${arch} && \
	echo "Packing..." && \
	tar czf ${packagename} qtcreator-gdb-${version}-${arch} && \
	mv ${targetdir}-${arch} ${targetdir}
