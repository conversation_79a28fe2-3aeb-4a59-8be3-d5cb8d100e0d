broot=${PWD}
source=${broot}/source
targets=x86_64-unknown-linux-gnu,i686-unknown-linux-gnu,arm-none-linux-gnueabi,x86_64-apple-darwin10
staging=${broot}/staging
pyversion=2.7
expatversion=2.1.1
arch=`uname -sm | sed 's/ /-/g' | tr A-Z a-z`
version=7.12
targetdir=${broot}/qtcreator-gdb-${version}
packagename=qtcreator-gdb-${version}-${arch}.tar.gz

all:package

clean:
	rm -rf  ${broot}/qtcreator-gdb-* ${staging}/gdb-* qtcreator-gdb-*.tar.gz

makesourcedir:
	@test -e ${source} || mkdir ${source}

maketargetdir:
	@test -e ${targetdir} || mkdir ${targetdir}

makestagingdir:
	@test -e ${staging} || mkdir ${staging}

${source}/expat-${expatversion}.tar.bz2: | makesourcedir
	cd ${source} && \
	echo "Downloading expat..." && \
	curl -LsOf http://sourceforge.net/projects/expat/files/expat/${expatversion}/expat-${expatversion}.tar.bz2 && \
	touch ${source}/expat-${expatversion}.tar.bz2

${source}/Python-${pyversion}.tgz: | makesourcedir
	cd ${source} && \
	echo "Downloading python..." && \
	curl -Osf http://www.python.org/ftp/python/${pyversion}/Python-${pyversion}.tgz && \
	touch ${source}/Python-${pyversion}.tgz

${source}/gdb-${version}.tar.xz: | makesourcedir
	cd ${source} && \
	echo "Downloading gdb..." && \
	curl -Osf http://ftp.gnu.org/gnu/gdb/gdb-${version}.tar.xz || \
	curl -osf gdb-${version}.tar.xz http://ftp.gnu.org/gnu/gdb/gdb-${version}a.tar.xz && \
	touch gdb-*.tar.xz

${staging}/lib/libexpat.a: ${source}/expat-${expatversion}.tar.bz2 | makestagingdir
	cd ${staging} && \
	echo "Extracting expat..." && \
	tar xf ${source}/expat-${expatversion}.tar.bz2 && cd expat-${expatversion} && \
	./configure --disable-shared -prefix=${staging} && ${MAKE} && ${MAKE} install

${staging}/lib/libpython${pyversion}.a: ${source}/Python-${pyversion}.tgz | makestagingdir
	unset PYTHONHOME && \
	cd ${staging} && \
	echo "Extracting python..." && \
	tar xf ${source}/Python-${pyversion}.tgz && cd Python-${pyversion} && \
	./configure --prefix=${staging} && ${MAKE} && ${MAKE} install

${targetdir}/python/include/python${pyversion}/pyconfig.h: ${staging}/lib/libpython${pyversion}.a | maketargetdir
	mkdir -p ${targetdir}/python/lib && cp -a ${staging}/lib/python${pyversion} ${targetdir}/python/lib/
	mkdir -p ${targetdir}/python/include/python${pyversion} && cp ${staging}/include/python${pyversion}/pyconfig.h ${targetdir}/python/include/python${pyversion}/

${staging}/gdb-${version}/configure: ${source}/gdb-${version}.tar.xz | makestagingdir
	cd ${staging} && \
	echo "Extracting gdb..." && \
	tar xf ${source}/gdb-${version}.tar.xz && \
	cd gdb-${version} && \
	touch configure

${targetdir}/gdb: ${staging}/gdb-${version}/configure ${staging}/lib/libpython${pyversion}.a ${staging}/lib/libexpat.a | maketargetdir
	test -e ${staging}/gdb-${version}-build || mkdir ${staging}/gdb-${version}-build
	export PYTHONHOME=${staging} && \
	export PATH="${staging}/bin/:$$PATH" && \
	cd ${staging}/gdb-${version}-build && \
	${staging}/gdb-${version}/configure --disable-nls --enable-targets=${targets} --with-separate-debug-dir="" \
        --disable-werror --with-python=${staging} --with-libexpat-prefix=${staging} && \
	${MAKE} MAKEFLAGS+=-j1 && \
	strip -o ${targetdir}/gdb gdb/gdb

package: ${targetdir}/gdb ${targetdir}/python/include/python${pyversion}/pyconfig.h
	mv ${targetdir} ${targetdir}-${arch}
	echo "Packing..."
	tar czf ${packagename} qtcreator-gdb-${version}-${arch}
	mv ${targetdir}-${arch} ${targetdir}
