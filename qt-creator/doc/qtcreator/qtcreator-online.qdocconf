# Run qdoc from the directory that contains this file.
include(config/qtcreator-project.qdocconf)

HTML.footer = \
    "   </div>\n" \
    "   <p class=\"copy-notice\">\n" \
    "   <acronym title=\"Copyright\">&copy;</acronym> $QTCREATOR_COPYRIGHT_YEAR The Qt Company Ltd.\n" \
    "   Documentation contributions included herein are the copyrights of\n" \
    "   their respective owners. " \
    "   The documentation provided herein is licensed under the terms of the" \
    "   <a href=\"http://www.gnu.org/licenses/fdl.html\">GNU Free Documentation" \
    "   License version 1.3</a> as published by the Free Software Foundation. " \
    "   Qt and respective logos are trademarks of The Qt Company Ltd " \
    "   in Finland and/or other countries worldwide. All other trademarks are property\n" \
    "   of their respective owners. </p>\n"

include($QT_INSTALL_DOCS/global/qt-html-templates-online.qdocconf)

# Override macros for online use
include(../config/macros-online.qdocconf)

# Add an .html file with sidebar content, used in the online style
HTML.stylesheets               += config/style/qt5-sidebar.html
