macro.aacute.HTML       = "&aacute;"
macro.Aring.HTML        = "&Aring;"
macro.aring.HTML        = "&aring;"
macro.Auml.HTML         = "&Auml;"
macro.author            = "\\b{Author:}"
macro.br.HTML           = "<br />"
macro.BR.HTML           = "<br />"
macro.copyright.HTML    = "&copy;"
macro.eacute.HTML       = "&eacute;"
macro.gui               = "\\b"
macro.hr.HTML           = "<hr />"
macro.iacute.HTML       = "&iacute;"
macro.key               = "\\b"
macro.menu              = "\\b"
macro.macos             = "macOS"
macro.note              = "\\b{Note:}"
macro.oslash.HTML       = "&oslash;"
macro.ouml.HTML         = "&ouml;"
macro.B2Q               = "Boot to Qt"
macro.Q3DS              = "Qt 3D Studio"
macro.QA                = "Qt Assistant"
macro.QAC               = "Qt Academy"
macro.QB                = "Qt Bridge"
macro.QBPS              = "Qt Bridge for Adobe Photoshop"
macro.QBXD              = "Qt Bridge for Adobe XD"
macro.QBSK              = "Qt Bridge for Sketch"
macro.QBF               = "Qt Bridge for Figma"
macro.QC                = "$IDE_DISPLAY_NAME"
macro.QCE               = "$IDE_DISPLAY_NAME Enterprise"
macro.QD                = "Qt Widgets Designer"
macro.QDS               = "Qt Design Studio"
macro.QQEM              = "Qt Quick Effect Maker"
macro.QDV               = "Qt Design Viewer"
macro.QL                = "Qt Linguist"
macro.QMCU              = "Qt for MCUs"
macro.QMLD              = "Qt Quick Designer"
macro.QMLLS             = "QML Language Server"
macro.QQV               = "Qt QML Viewer"
macro.QSDK              = "Qt"
macro.QUL               = "Qt Quick Ultralite"
macro.QUV               = "Qt UI Viewer"
macro.QOI               = "Qt Online Installer"
macro.QMT               = "Qt Maintenance Tool"
macro.qtcversion        = $QTC_VERSION
macro.param             = "\\e"
macro.preferences       = "\\l{Find preferences}{Preferences}"
macro.raisedaster.HTML  = "<sup>*</sup>"
macro.rarrow.HTML       = "&rarr;"
macro.reg.HTML          = "<sup>&reg;</sup>"
macro.return            = "Returns"
macro.starslash         = "\\c{*/}"
macro.begincomment      = "\\c{/*}"
macro.endcomment        = "\\c{*/}"
macro.uuml.HTML         = "&uuml;"
macro.mdash.HTML        = "&mdash;"
macro.commercial        = "\\raw HTML\n<p><a title=\"Available under certain Qt licenses.\"><img src=\"images/commercial.png\"/></a></p>\n\\endraw"

macro.beginfloatleft.HTML   = "<div style=\"float: left; margin-right: 2em\">"
macro.beginfloatright.HTML  = "<div style=\"float: right; margin-left: 2em\">"
macro.endfloat.HTML         = "</div>"
macro.clearfloat.HTML       = "<br style=\"clear: both\" />"
macro.emptyspan.HTML	    = "<span></span>"
macro.externallink.HTML     = "<a href=\"\1\" target=\"_blank\">\2</a>"

# Embed YouTube content by video ID - Example: \youtube dQw4w9WgXcQ
# Also requires a <ID>.jpg thumbnail for offline docs. In .qdocconf, add:
#
# HTML.extraimages           += images/dQw4w9WgXcQ.jpg
# qhp.ProjectName.extraFiles += images/dQw4w9WgXcQ.jpg
#
macro.youtube.HTML = "<div class=\"video\">\n" \
                     "<a href=\"https://www.youtube.com/watch/?v=\1\">\n"\
                     "<img src=\"images/\1.jpg\"" \
                     "title=\"Click to play in a browser\" /></a>\n" \
                     "</div>\n"
