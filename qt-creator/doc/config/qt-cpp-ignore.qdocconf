Cpp.ignoretokens        = QAXFACTORY_EXPORT \
                          QDESIGNER_COMPONENTS_LIBRARY \
                          QDESIGNER_EXTENSION_LIBRARY \
                          QDESIGNER_SDK_LIBRARY \
                          QDESIGNER_SHARED_LIBRARY \
                          QDESIGNER_UILIB_LIBRARY \
                          QM_EXPORT_CANVAS \
                          QM_EXPORT_DNS \
                          QM_EXPORT_DOM \
                          QM_EXPORT_FTP \
                          QM_EXPORT_HTTP \
                          QM_EXPORT_ICONVIEW \
                          QM_EXPORT_NETWORK \
                          QM_EXPORT_OPENGL \
                          QM_EXPORT_OPENVG \
                          QM_EXPORT_SQL \
                          QM_EXPORT_TABLE \
                          QM_EXPORT_WORKSPACE \
                          QM_EXPORT_XML \
                          QT_ASCII_CAST_WARN \
                          QT_ASCII_CAST_WARN_CONSTRUCTOR \
                          QT_BEGIN_HEADER \
                          QT_DESIGNER_STATIC \
                          QT_END_HEADER \
                          QT_FASTCALL \
                          QT_WIDGET_PLUGIN_EXPORT \
                          Q_COMPAT_EXPORT \
                          Q_CORE_EXPORT \
                          Q_CORE_EXPORT_INLINE \
                          Q_EXPLICIT \
                          Q_EXPORT \
                          Q_EXPORT_CODECS_CN \
                          Q_EXPORT_CODECS_JP \
                          Q_EXPORT_CODECS_KR \
                          Q_EXPORT_PLUGIN \
                          Q_GFX_INLINE \
                          Q_AUTOTEST_EXPORT \
                          Q_GUI_EXPORT \
                          Q_GUI_EXPORT_INLINE \
                          Q_GUI_EXPORT_STYLE_CDE \
                          Q_GUI_EXPORT_STYLE_COMPACT \
                          Q_GUI_EXPORT_STYLE_MAC \
                          Q_GUI_EXPORT_STYLE_MOTIF \
                          Q_GUI_EXPORT_STYLE_MOTIFPLUS \
                          Q_GUI_EXPORT_STYLE_PLATINUM \
                          Q_GUI_EXPORT_STYLE_POCKETPC \
                          Q_GUI_EXPORT_STYLE_SGI \
                          Q_GUI_EXPORT_STYLE_WINDOWS \
                          Q_GUI_EXPORT_STYLE_WINDOWSXP \
                          QHELP_EXPORT \
                          Q_INLINE_TEMPLATE \
                          Q_INTERNAL_WIN_NO_THROW \
                          Q_NETWORK_EXPORT \
                          Q_OPENGL_EXPORT \
                          Q_OPENVG_EXPORT \
                          Q_OUTOFLINE_TEMPLATE \
                          Q_SQL_EXPORT \
                          Q_SVG_EXPORT \
                          Q_SCRIPT_EXPORT \
                          Q_SCRIPTTOOLS_EXPORT \
                          Q_TESTLIB_EXPORT \
                          Q_TYPENAME \
                          Q_XML_EXPORT \
                          Q_XMLSTREAM_EXPORT \
                          Q_XMLPATTERNS_EXPORT \
                          QDBUS_EXPORT \
                          Q_DBUS_EXPORT \
                          QT_BEGIN_NAMESPACE \
                          QT_BEGIN_INCLUDE_NAMESPACE \
                          QT_END_NAMESPACE \
                          QT_END_INCLUDE_NAMESPACE \
                          PHONON_EXPORT \
                          Q_DECLARATIVE_EXPORT \
                          Q_GADGET \
                          QWEBKIT_EXPORT \
                          Q_INVOKABLE \
                          EXTENSIONSYSTEM_EXPORT
Cpp.ignoredirectives    = Q_DECLARE_HANDLE \
                          Q_DECLARE_INTERFACE \
                          Q_DECLARE_METATYPE \
                          Q_DECLARE_OPERATORS_FOR_FLAGS \
                          Q_DECLARE_PRIVATE \
                          Q_DECLARE_PUBLIC \
                          Q_DECLARE_SHARED \
                          Q_DECLARE_TR_FUNCTIONS \
                          Q_DECLARE_TYPEINFO \
                          Q_DISABLE_COPY \
                          QT_FORWARD_DECLARE_CLASS \
                          Q_DUMMY_COMPARISON_OPERATOR \
                          Q_ENUMS \
                          Q_FLAGS \
                          Q_INTERFACES \
                          __attribute__ \
                          K_DECLARE_PRIVATE \
                          PHONON_OBJECT \
                          PHONON_HEIR \
			  Q_PRIVATE_PROPERTY \
			  Q_DECLARE_PRIVATE_D \
			  Q_CLASSINFO
