// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GPL-3.0-only WITH Qt-GPL-exception-1.0

#pragma once

namespace QmlProjectManager::Constants {

const char customFileSelectorsData[] = "CustomFileSelectorsData";
const char supportedLanguagesData[] = "SupportedLanguagesData";
const char primaryLanguageData[] = "PrimaryLanguageData";
const char customForceFreeTypeData[] = "CustomForceFreeType";
const char customQtForMCUs[] = "CustomQtForMCUs";
const char customQt6Project[] = "CustomQt6Project";

const char mainFilePath[] = "MainFilePath";
const char canonicalProjectDir[] ="CanonicalProjectDir";

const char enviromentLaunchedQDS[] = "QTC_LAUNCHED_QDS";

const char ALWAYS_OPEN_UI_MODE[] = "J.QtQuick/QmlJSEditor.openUiQmlMode";
const char QML_RESOURCE_PATH[] = "qmldesigner/propertyEditorQmlSources/imports";
const char LANDING_PAGE_PATH[] = "qmldesigner/landingpage";

const char QML_PROJECT_ID[] = "QmlProjectManager.QmlProject";
const char QML_RUNCONFIG_ID[] = "QmlProjectManager.QmlRunConfiguration.Qml";
const char QML_VIEWER_KEY[] = "QmlProjectManager.QmlRunConfiguration.QDeclarativeViewer";
const char QML_VIEWER_ARGUMENTS_KEY[] = "QmlProjectManager.QmlRunConfiguration.QDeclarativeViewerArguments";
const char QML_VIEWER_TARGET_DISPLAY_NAME[] = "QML Viewer";
const char QML_MAINSCRIPT_KEY[] = "QmlProjectManager.QmlRunConfiguration.MainScript";
const char USE_MULTILANGUAGE_KEY[] = "QmlProjectManager.QmlRunConfiguration.UseMultiLanguage";
const char LAST_USED_LANGUAGE[] = "QmlProjectManager.QmlRunConfiguration.LastUsedLanguage";
const char USER_ENVIRONMENT_CHANGES_KEY[] = "QmlProjectManager.QmlRunConfiguration.UserEnvironmentChanges";

const char EXPORT_MENU[] = "QmlDesigner.ExportMenu";
const char G_EXPORT_GENERATE[] = "QmlDesigner.Group.GenerateProject";
const char G_EXPORT_CONVERT[] = "QmlDesigner.Group.ConvertProject";

} // QmlProjectManager::Constants
