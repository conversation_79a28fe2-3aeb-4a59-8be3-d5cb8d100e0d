// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GPL-3.0-only WITH Qt-GPL-exception-1.0

#pragma once

#include "propertyprovider.h"

namespace QbsProjectManager {
namespace Internal {

class DefaultPropertyProvider : public PropertyProvider
{
    Q_OBJECT

public:
    bool canHandle(const ProjectExplorer::Kit *k) const override { return k; }
    QVariantMap properties(const ProjectExplorer::Kit *k,
                           const QVariantMap &defaultData) const override;

private:
    QVariantMap autoGeneratedProperties(const ProjectExplorer::Kit *k,
                                        const QVariantMap &defaultData) const;
};

} // namespace Internal
} // namespace QbsProjectManager
