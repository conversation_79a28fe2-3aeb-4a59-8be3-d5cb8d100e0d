{
    "Name" : "Python",
    "Version" : "${IDE_VERSION}",
    "CompatVersion" : "${IDE_VERSION_COMPAT}",
    "Vendor" : "The Qt Company Ltd",
    "Copyright" : "(C) ${IDE_COPYRIGHT_YEAR} The Qt Company Ltd",
    "License" : [ "Commercial Usage",
                  "",
                  "Licensees holding valid Qt Commercial licenses may use this plugin in accordance with the Qt Commercial License Agreement provided with the Software or, alternatively, in accordance with the terms contained in a written agreement between you and The Qt Company.",
                  "",
                  "GNU General Public License Usage",
                  "",
                  "Alternatively, this plugin may be used under the terms of the GNU General Public License version 3 as published by the Free Software Foundation with exceptions as appearing in the file LICENSE.GPL3-EXCEPT included in the packaging of this plugin. Please review the following information to ensure the GNU General Public License requirements will be met: https://www.gnu.org/licenses/gpl-3.0.html."
    ],
    "Category" : "Other Languages",
    "Description" : "Develop applications using the Qt bindings for the Python programming language.",
    "LongDescription" : [
        "You also need:",
            "- Qt for Python",
            "- Tools for Python development"
    ],
    "Url" : "https://www.qt.io",
    ${IDE_PLUGIN_DEPENDENCIES},

    "Mimetypes" : [
        "<?xml version='1.0'?>",
        "<mime-info xmlns='http://www.freedesktop.org/standards/shared-mime-info'>",
        "    <mime-type type='text/x-python-gui'>",
        "        <sub-class-of type='text/x-python'/>",
        "        <comment>Python source file without console</comment>",
        "        <glob pattern='*.pyw'/>",
        "    </mime-type>",
        "    <mime-type type='text/x-python-interface'>",
        "        <sub-class-of type='text/x-python'/>",
        "        <comment>Python module interface file</comment>",
        "        <glob pattern='*.pyi'/>",
        "    </mime-type>",
        "    <mime-type type='text/x-pyqt-project'>",
        "        <sub-class-of type='text/x-python'/>",
        "        <comment>Qt Creator Python project file</comment>",
        "        <glob pattern='*.pyqtc'/>",
        "    </mime-type>",
        "    <mime-type type='text/x-python-project'>",
        "        <sub-class-of type='application/json'/>",
        "        <comment>Qt Creator Python project file</comment>",
        "        <glob pattern='*.pyproject'/>",
        "    </mime-type>",
        "</mime-info>"
    ]
}
