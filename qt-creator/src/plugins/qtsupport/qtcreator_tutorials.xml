<?xml version="1.0" encoding="utf-8"?>
<instructionals module="Qt">
  <categories>
    <category>Help</category>
    <category>Learning</category>
    <category>Online</category>
    <category>Talk</category>
  </categories>
  <tutorials>
    <tutorial imageUrl=":qtsupport/images/icons/tutorialicon.png" difficulty="" docUrl="qthelp://org.qt-project.qtcreator/doc/creator-build-example-application.html" projectPath="" name="Build and run">
      <description><![CDATA[Testing that your installation is successful by opening an existing example application project.]]></description>
      <tags>qt creator,build,compile,help</tags>
      <meta>
        <entry name="category">Help</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/tutorialicon.png" difficulty="" docUrl="qthelp://org.qt-project.qtcreator/doc/creator-writing-program.html" projectPath="" name="Qt Widgets application">
      <description><![CDATA[Using Qt Creator to create a small Qt application, Text Finder.]]></description>
      <tags>qt creator,qt widgets designer,widgets,c++,text,help</tags>
      <meta>
        <entry name="category">Help</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/tutorialicon.png" difficulty="" docUrl="qthelp://org.qt-project.qtdoc/qtwidgets/qtwidgets-tutorials-notepad-example.html" projectPath="" name="Getting Started Programming with Qt Widgets">
      <description><![CDATA[Developing Qt applications using C++ and the Qt Widgets module.]]></description>
      <tags>qt,qt creator,qt widgets designer,widgets,c++,help</tags>
      <meta>
        <entry name="category">Help</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/tutorialicon.png" difficulty="" docUrl="qthelp://org.qt-project.qtcreator/doc/qtcreator-transitions-example.html" projectPath="" name="Qt Quick application">
      <description><![CDATA[Using basic QML types and learning about basic concepts of Qt Quick.]]></description>
      <tags>qt creator,qt quick,qml,states,transitions,help</tags>
      <meta>
        <entry name="category">Help</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/tutorialicon.png" difficulty="" docUrl="qthelp://org.qt-project.qtcreator/doc/qtcreator-accelbubble-example.html" projectPath="" name="Mobile application">
      <description><![CDATA[Developing Qt Quick applications for Android and iOS devices.]]></description>
      <tags>qt creator,qml,android,ios,controls,help</tags>
      <meta>
        <entry name="category">Help</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/tutorialicon.png" difficulty="" docUrl="qthelp://org.qt-project.qtcreator/doc/creator-tutorial-python-application-qt-quick.html" projectPath="" name="Qt Quick and Python">
      <description><![CDATA[Using Python bindings to develop Qt Quick applications.]]></description>
      <tags>qt creator,qt quick,python,help</tags>
      <meta>
        <entry name="category">Help</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/tutorialicon.png" difficulty="" docUrl="qthelp://org.qt-project.qtcreator/doc/creator-tutorial-python-application-qt-widgets.html" projectPath="" name="Qt Widgets and Python">
      <description><![CDATA[Using Python bindings to develop Qt applications using the Qt Widgets module.]]></description>
      <tags>qt creator,widgets,python,help</tags>
      <meta>
        <entry name="category">Help</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/tutorialicon.png" difficulty="" docUrl="qthelp://org.qt-project.qtcreator/doc/creator-tutorial-python-application-qt-widgets-ui.html" projectPath="" name="Qt Widgets UI and Python">
      <description><![CDATA[Using Python bindings and Qt Widgets Designer to develop Qt Widgets-based UIs.]]></description>
      <tags>qt creator,qt widgets designer,widgets,python,help</tags>
      <meta>
        <entry name="category">Help</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/tutorialicon.png" difficulty="" docUrl="qthelp://org.qt-project.qtcreator/doc/creator-debugging-example.html" projectPath="" name="C++ debugging">
      <description><![CDATA[Debugging Qt C++ applications in the Qt Creator Debug mode.]]></description>
      <tags>qt creator,c++,debugging,help</tags>
      <meta>
        <entry name="category">Help</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/tutorialicon.png" difficulty="" docUrl="qthelp://org.qt-project.qtcreator/doc/creator-qml-debugging-example.html" projectPath="" name="Qt Quick debugging">
      <description><![CDATA[Debugging Qt Quick applications in the Qt Creator Debug mode.]]></description>
      <tags>qt creator,qt quick,debugging,help</tags>
      <meta>
        <entry name="category">Help</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/tutorialicon.png" difficulty="" docUrl="qthelp://org.qt-project.qtdoc/qtdoc/qtdoc-tutorials-alarms-example.html" projectPath="" name="Getting Started Programming with Qt Quick">
      <description><![CDATA[Developing Qt Quick applications using Qt Quick and Qt Quick Controls.]]></description>
      <tags>qt quick,controls,tumbler,help</tags>
      <meta>
        <entry name="category">Help</entry>
      </meta>
    </tutorial>

    <tutorial imageUrl=":qtsupport/images/icons/gs-qtc-14.png" difficulty="" projectPath="" name="Getting Started with Qt Creator 14" isVideo="true" videoUrl="https://www.qt.io/academy/course-catalog#getting-started-with-qt-creator-14" videoLength="20:00">
      <description><![CDATA[Learn about Qt Creator 14.]]></description>
      <tags>qt creator,ui,welcome,qt widgets designer,widgets,editing,debugging,learning,2024</tags>
      <meta>
        <entry name="category">Learning</entry>
      </meta>
    </tutorial>

    <tutorial imageUrl=":qtsupport/images/icons/youtube9xqhq9nDiOg.webp" difficulty="" projectPath="" name="Qt SCXML and State Machine Tooling in Qt Creator" isVideo="true" videoUrl="https://youtu.be/9xqhq9nDiOg" videoLength="4:53">
      <description><![CDATA[Creating state machines.]]></description>
      <tags>qt creator,SCXML,video</tags>
      <meta>
        <entry name="category">Online</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubexNIz78IPBu0.webp" difficulty="" projectPath="" name="How to do translations with Qt Linguist" isVideo="true" videoUrl="https://youtu.be/xNIz78IPBu0" videoLength="9:14">
      <description><![CDATA[Preparing applications for translation, translating them with Qt Linguist, and using the translations in apps.]]></description>
      <tags>qt creator,qt linguist,translation,video,2021</tags>
      <meta>
        <entry name="category">Online</entry>
      </meta>
    </tutorial>

    <tutorial imageUrl=":qtsupport/images/icons/youtubeKo3DuCgFamo.webp" difficulty="" projectPath="" name="Custom Qt Creator Wizards" isVideo="true" videoUrl="https://www.youtube.com/watch?v=Ko3DuCgFamo" videoLength="27:21">
      <description><![CDATA[Adding custom file and project creation wizards to Qt Creator.]]></description>
      <tags>qt creator,wizard,talk,2015</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubeDP0lMoLVneY.webp" difficulty="" projectPath="" name="Extending Qt Creator" isVideo="true" videoUrl="http://www.youtube.com/watch?v=DP0lMoLVneY" videoLength="59:49">
      <description><![CDATA[Customizing Qt Creator to fit your own or your customers' purposes.]]></description>
      <tags>qt creator,configuration,talk,2013</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubePzV2MYRAUYQ.webp" difficulty="" projectPath="" name="How to create a plugin for Qt Creator" isVideo="true" videoUrl="https://youtu.be/PzV2MYRAUYQ" videoLength="55:37">
      <description><![CDATA[Adding plugins to Qt Creator.]]></description>
      <tags>qt creator,plugins,talk,2019</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubeTiJiF0MOOFc.webp" difficulty="" projectPath="" name="Qt Creator - Using the QML Profiler" isVideo="true" videoUrl="https://www.youtube.com/watch?v=TiJiF0MOOFc" videoLength="55:12">
      <description><![CDATA[Monitoring the performance of a Qt Quick application.]]></description>
      <tags>qt quick,qt creator,qml profiler,talk,2014</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubeG0AbgVHGdXI.webp" difficulty="" projectPath="" name="The CPU Usage Analyzer for Device Creation" isVideo="true" videoUrl="https://www.youtube.com/watch?v=G0AbgVHGdXI" videoLength="22:30">
      <description><![CDATA[Using the Linux perf tool to generate data for code analysis.]]></description>
      <tags>qt creator,cpu usage analyzer,perf,embedded,device creation,talk,2015</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubeX0kEkB0ewyw.webp" difficulty="" projectPath="" name="Qt SCXML - State Machines Made Easier" isVideo="true" videoUrl="https://youtu.be/X0kEkB0ewyw" videoLength="42:22">
      <description><![CDATA[Using the Qt SCXML module and Qt Creator SCXML editor.]]></description>
      <tags>qt creator,scxml,talk,2016</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubev4glCQt2jE0.webp" difficulty="" projectPath="" name="Effective Multi-Platform Development with Qt Creator, QBS, and QEMU" isVideo="true" videoUrl="https://www.youtube.com/watch?v=v4glCQt2jE0" videoLength="19:08">
      <description><![CDATA[Using Qt Creator, Qbs, and QEMU for application development.]]></description>
      <tags>qt creator,qbs,qemu,talk,2015</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubeT_13aX5NTPk.webp" difficulty="" projectPath="" name="Qt for iOS - A to Z" isVideo="true" videoUrl="https://youtu.be/T_13aX5NTPk" videoLength="1:00:13">
      <description><![CDATA[Developing Qt applications for iOS.]]></description>
      <tags>qt creator,ios,talk,2016</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubehrKz63Q_Rf0.webp" difficulty="" projectPath="" name="Qt Creator for Bare Metal Development" isVideo="true" videoUrl="http://www.youtube.com/watch?v=hrKz63Q_Rf0" videoLength="9:35">
      <description><![CDATA[Developing Qt Applications for Bare Metal devices.]]></description>
      <tags>qt creator,baremetal,talk,2013</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubeWIRRoPxIerc.webp" difficulty="" projectPath="" name="The Curse of Choice - An Overview of GUI Technologies in Qt" isVideo="true" videoUrl="https://youtu.be/WIRRoPxIerc" videoLength="40:45">
      <description><![CDATA[Overview of UI technologies that can be used with Qt.]]></description>
      <tags>qt quick,ui,widgets,talk,2016</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubesRihJdZFuCg.webp" difficulty="" projectPath="" name="Code Once Deploy Everywhere: How Qt is ideal for cross-platform development" isVideo="true" videoUrl="https://youtu.be/sRihJdZFuCg" videoLength="42:37">
      <description><![CDATA[Using Qt Creator for cross-platform development.]]></description>
      <tags>qt creator,talk,2019</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubeW3WC-VpKdGQ.webp" difficulty="" projectPath="" name="WEBASM with Qt - Qt for WebAssembly" isVideo="true" videoUrl="https://youtu.be/W3WC-VpKdGQ" videoLength="27:50">
      <description><![CDATA[Running Qt applications on the Web using Qt for WebAssembly.]]></description>
      <tags>qt creator,webassembly,emscripten,talk,2019</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubeqclquZ99ZVQ.webp" difficulty="" projectPath="" name="How to Develop with Qt for Multiple Screen Resolutions and Platforms and Best Practices for an Efficient App Lifecycle with Qt" isVideo="true" videoUrl="https://youtu.be/qclquZ99ZVQ" videoLength="27:44">
      <description><![CDATA[Best practices for an efficient app lifecycle.]]></description>
      <tags>qt,qt quick,screen resolution,ui,talk,2016</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubeB0X5FOev9Lw.webp" difficulty="" projectPath="" name="Qt Widgets Designer tutorial: Integrate custom widgets" isVideo="true" videoUrl="https://youtu.be/B0X5FOev9Lw" videoLength="27:07">
      <description><![CDATA[Integrating custom widgets into Qt Widgets Designer.]]></description>
      <tags>qt widgets designer,widgets,ui,talk,2019</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtube3o2Wo4YzlII.webp" difficulty="" projectPath="" name="Android &amp; iOS - Put Your App on a Diet" isVideo="true" videoUrl="https://www.youtube.com/watch?v=3o2Wo4YzlII" videoLength="23:41">
      <description><![CDATA[Making Android and iOS apps smaller.]]></description>
      <tags>android,ios,talk,2017</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubev_ynSET9FHU.webp" difficulty="" projectPath="" name="LTTng for full stack tracing" isVideo="true" videoUrl="https://youtu.be/v_ynSET9FHU" videoLength="25:46">
      <description><![CDATA[Using tracing and profiling to optimize the startup time of apps.]]></description>
      <tags>qt creator,qml profiler,ctf viewer,lttng,talk,2019</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubeau3brB7lNms.webp" difficulty="" projectPath="" name="No Limits - How to Make a More Complicated Mobile Business App" isVideo="true" videoUrl="https://www.youtube.com/watch?v=au3brB7lNms" videoLength="23:33">
      <description><![CDATA[Creating mobile business apps using Qt Quick Controls 2.]]></description>
      <tags>android,ios,qt quick,controls,talk,2017</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubeECA8_oLT0ZE.webp" difficulty="" projectPath="" name="Qt &amp; Yocto, an ECU development workflow" isVideo="true" videoUrl="https://youtu.be/ECA8_oLT0ZE" videoLength="29:08">
      <description><![CDATA[Using Qt Creator kits and Yocto when developing for embedded devices.]]></description>
      <tags>qt creator,kits,yocto,embedded,talk,2019</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtube1w0ak9RNNWY.webp" difficulty="" projectPath="" name="Qt Creator in Space" isVideo="true" videoUrl="https://youtu.be/1w0ak9RNNWY" videoLength="28:05">
      <description><![CDATA[Creating and maintaining a portfolio of Qt Creator plugins.]]></description>
      <tags>qt creator,plugins,video,2021</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubenmvurCcsWos.webp" difficulty="" projectPath="" name="All You Need to Get Your App Done with Qt for Android" isVideo="true" videoUrl="https://youtu.be/nmvurCcsWos" videoLength="24:11">
      <description><![CDATA[Developing an Android app using Qt for Android.]]></description>
      <tags>android,talk,2021</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubetnZo9umrPtg.webp" difficulty="" projectPath="" name="Styling a Qt Quick Controls Desktop Application" isVideo="true" videoUrl="https://youtu.be/tnZo9umrPtg" videoLength="29:40">
      <description><![CDATA[Styling Qt Quick Controls using the styling API.]]></description>
      <tags>qt quick,controls,styling,ui,talk,2021</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    <tutorial imageUrl=":qtsupport/images/icons/youtubepN0pRBUqrrc.webp" difficulty="" projectPath="" name="The New Property Bindings: Making C++ more QMLish" isVideo="true" videoUrl="https://youtu.be/pN0pRBUqrrc" videoLength="29:54">
      <description><![CDATA[Using the Qt 6 property system in pure C++ and mixed C++/QML applications.]]></description>
      <tags>qt,c++,qml,talk,2021</tags>
      <meta>
        <entry name="category">Talk</entry>
      </meta>
    </tutorial>
    </tutorials>
</instructionals>
