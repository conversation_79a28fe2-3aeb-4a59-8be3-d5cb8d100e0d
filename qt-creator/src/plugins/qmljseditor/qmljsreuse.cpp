// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GPL-3.0-only WITH Qt-GPL-exception-1.0

#include "qmljsreuse.h"

#include <QChar>
#include <QPainter>

namespace QmlJSEditor {
namespace Internal {

bool isIdentifierChar(const QChar &c, bool atStart, bool acceptDollar)
{
    switch (c.unicode()) {
    case '_':
        return true;
    case '$':
        if (acceptDollar)
            return true;
        return false;

    default:
        if (atStart)
            return c.isLetter();
        else
            return c.isLetterOrNumber();
    }
}

bool isValidFirstIdentifierChar(const QChar &ch) {
    return ch.isLetter() || ch == QLatin1Char('_') || ch.isHighSurrogate()
            || ch.isLowSurrogate();
}

bool isValidIdentifierChar(const QChar &ch) {
    return isValidFirstIdentifierChar(ch) || ch.isNumber();
}

bool isDelimiterChar(const QChar &c)
{
    switch (c.unicode()) {
    case '{':
    case '}':
    case '[':
    case ']':
    case ')':
    case '?':
    case '!':
    case ':':
    case ';':
    case ',':
    case '+':
    case '-':
    case '*':
    case '/':
        return true;

    default:
        return false;
    }
}

bool isActivationChar(const QChar &c)
{
    if (c == QLatin1Char('(') || c == QLatin1Char('.') || c == QLatin1Char('/'))
        return true;
    return false;
}

QIcon iconForColor(const QColor &color)
{
    QPixmap pix(6, 6);

    int pixSize = 20;
    QBrush br(color);

    QPixmap pm(2 * pixSize, 2 * pixSize);
    QPainter pmp(&pm);
    pmp.fillRect(0, 0, pixSize, pixSize, Qt::lightGray);
    pmp.fillRect(pixSize, pixSize, pixSize, pixSize, Qt::lightGray);
    pmp.fillRect(0, pixSize, pixSize, pixSize, Qt::darkGray);
    pmp.fillRect(pixSize, 0, pixSize, pixSize, Qt::darkGray);
    pmp.fillRect(0, 0, 2 * pixSize, 2 * pixSize, color);
    br = QBrush(pm);

    QPainter p(&pix);
    int corr = 1;
    QRect r = pix.rect().adjusted(corr, corr, -corr, -corr);
    p.setBrushOrigin((r.width() % pixSize + pixSize) / 2 + corr, (r.height() % pixSize + pixSize) / 2 + corr);
    p.fillRect(r, br);

    p.fillRect(r.width() / 4 + corr, r.height() / 4 + corr,
               r.width() / 2, r.height() / 2,
               QColor(color.rgb()));
    p.drawRect(pix.rect().adjusted(0, 0, -1, -1));

    return pix;
}


} // Internal
} // QmlJSEditor
