// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GPL-3.0-only WITH Qt-GPL-exception-1.0

#pragma once

#include <QtGlobal>

namespace QmlJSEditor {
namespace Constants {

const char M_CONTEXT[] = "QML JS Editor.ContextMenu";

const char M_REFACTORING_MENU_INSERTION_POINT[] = "QmlJSEditor.RefactorGroup";

const char C_QMLJSEDITOR_ID[] = "QmlJSEditor.QMLJSEditor";
const char C_QTQUICKDESIGNEREDITOR_ID[] = "QmlJSEditor.QtQuickDesignerEditor";
const char SETTINGS_CATEGORY_QML[] = "J.QtQuick";

const char SHOW_QT_QUICK_HELPER[] = "QmlJSEditor.ShowQtQuickHelper";

const char TASK_CATEGORY_QML[] = "Task.Category.Qml";
const char TASK_CATEGORY_QML_ANALYSIS[] = "Task.Category.QmlAnalysis";

const char QML_SNIPPETS_GROUP_ID[] = "QML";

const char QMLLINT_BUILD_TARGET[] = "all_qmllint";

} // namespace Constants
} // namespace QmlJSEditor
