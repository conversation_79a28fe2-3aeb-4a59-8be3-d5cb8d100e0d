// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GPL-3.0-only WITH Qt-GPL-exception-1.0

#pragma once

#include <QtGlobal>

namespace QmlJSTools {
namespace Constants {

const char QML_JS_CODE_STYLE_SETTINGS_ID[] = "A.Code Style";
const char QML_JS_CODE_STYLE_SETTINGS_NAME[] = QT_TRANSLATE_NOOP("QtC::QmlJSTools", "Code Style");

const char QML_JS_SETTINGS_ID[] = "QmlJS";

const char M_TOOLS_QMLJS[] = "QmlJSTools.Tools.Menu";
const char RESET_CODEMODEL[] = "QmlJSTools.ResetCodeModel";

} // namespace Constants
} // namespace QmlJSTools
