{
    "Name" : "QmlJSTools",
    "Version" : "${IDE_VERSION}",
    "CompatVersion" : "${IDE_VERSION_COMPAT}",
    "Vendor" : "The Qt Company Ltd",
    "Copyright" : "(C) ${IDE_COPYRIGHT_YEAR} The Qt Company Ltd",
    "License" : [ "Commercial Usage",
                  "",
                  "Licensees holding valid Qt Commercial licenses may use this plugin in accordance with the Qt Commercial License Agreement provided with the Software or, alternatively, in accordance with the terms contained in a written agreement between you and The Qt Company.",
                  "",
                  "GNU General Public License Usage",
                  "",
                  "Alternatively, this plugin may be used under the terms of the GNU General Public License version 3 as published by the Free Software Foundation with exceptions as appearing in the file LICENSE.GPL3-EXCEPT included in the packaging of this plugin. Please review the following information to ensure the GNU General Public License requirements will be met: https://www.gnu.org/licenses/gpl-3.0.html."
    ],
    "Category" : "Qt Quick",
    "Description" : "Tools for analyzing Qml/JS code.",
    "Url" : "https://www.qt.io",
    ${IDE_PLUGIN_DEPENDENCIES},

    "Mimetypes" : [
        "<?xml version='1.0'?>",
        "<mime-info xmlns='http://www.freedesktop.org/standards/shared-mime-info'>",
        "    <mime-type type='text/x-qml'>",
        "        <alias type='application/x-qml'/>",
        "        <!-- sub class is missing in the freedesktop.org definition -->",
        "        <sub-class-of type='text/plain'/>",
        "        <comment>QML file</comment>",
        "        <glob pattern='*.qml' weight='70'/>",
        "    </mime-type>",
        "    <mime-type type='application/x-qt.qbs+qml'>",
        "        <alias type='text/x-qt.qbs+qml'/>",
        "        <sub-class-of type='text/x-qml'/>",
        "        <comment>Qt Build Suite file</comment>",
        "        <glob pattern='*.qbs' weight='70'/>",
        "    </mime-type>",
        "    <mime-type type='application/x-qt.ui+qml'>",
        "        <alias type='text/x-qt.ui+qml'/>",
        "        <sub-class-of type='text/x-qml'/>",
        "        <comment>QtQuick Designer ui file</comment>",
        "        <glob pattern='*.ui.qml' weight='70'/>",
        "    </mime-type>",
        "    <mime-type type='application/x-qmlproject'>",
        "        <alias type='text/x-qmlproject'/>",
        "        <sub-class-of type='text/x-qml'/>",
        "        <comment>Qt Creator Qt UI project file</comment>",
        "        <glob pattern='*.qmlproject' weight='70'/>",
        "    </mime-type>",
        "    <mime-type type='application/x-qt.meta-info+qml'>",
        "        <alias type='text/x-qt.meta-info+qml'/>",
        "        <sub-class-of type='text/x-qml'/>",
        "        <comment>QML file</comment>",
        "        <glob pattern='*.qmltypes' weight='70'/>",
        "    </mime-type>",
        "    <mime-type type='application/json'>",
        "        <sub-class-of type='text/plain'/>",
        "        <comment>JSON file</comment>",
        "        <glob pattern='*.json' weight='70'/>",
        "    </mime-type>",
        "    <mime-type type='application/x-javascript-module'>",
        "        <sub-class-of type='application/javascript'/>",
        "        <comment>Javascript module</comment>",
        "        <glob pattern='*.mjs' weight='70'/>",
        "    </mime-type>",
        "    <mime-type type='text/x-qtscript'>",
        "        <alias type='application/x-qtscript'/>",
        "        <sub-class-of type='application/javascript'/>",
        "        <comment>Qt Script file</comment>",
        "        <glob pattern='*.qs' weight='70'/>",
        "    </mime-type>",
        "</mime-info>"
    ]

}
