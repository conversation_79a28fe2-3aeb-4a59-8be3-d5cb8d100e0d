// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR GPL-3.0-only WITH Qt-GPL-exception-1.0

#pragma once

#include <utils/icon.h>

namespace QmlDesigner {
namespace Icons {

const Utils::Icon ARROW_UP({
        {":/navigator/icon/arrowup.png", Utils::Theme::IconsBaseColor}});
const Utils::Icon ARROW_RIGHT({
        {":/navigator/icon/arrowright.png", Utils::Theme::IconsBaseColor}});
const Utils::Icon ARROW_DOWN({
        {":/navigator/icon/arrowdown.png", Utils::Theme::IconsBaseColor}});
const Utils::Icon ARROW_LEFT({
        {":/navigator/icon/arrowleft.png", Utils::Theme::IconsBaseColor}});
const Utils::Icon EXPORT_CHECKED(":/navigator/icon/export_checked.png");
const Utils::Icon EXPORT_UNCHECKED(":/navigator/icon/export_unchecked.png");
const Utils::Icon SNAPPING({
        {":/icon/layout/snapping.png", Utils::Theme::IconsBaseColor}});
const Utils::Icon NO_SNAPPING({
        {":/icon/layout/no_snapping.png", Utils::Theme::IconsBaseColor}});
const Utils::Icon NO_SNAPPING_AND_ANCHORING({
        {":/icon/layout/snapping_and_anchoring.png", Utils::Theme::IconsBaseColor}});

const Utils::Icon EDIT3D_LIGHT_ON({
        {":/edit3d/images/edit_light_on.png", Utils::Theme::QmlDesigner_HighlightColor}});
const Utils::Icon EDIT3D_LIGHT_OFF({
        {":/edit3d/images/edit_light_off.png", Utils::Theme::IconsBaseColor}});
const Utils::Icon EDIT3D_PARTICLE_ON({
        {":/edit3d/images/particles_on.png", Utils::Theme::QmlDesigner_HighlightColor}});
const Utils::Icon EDIT3D_PARTICLE_OFF({
        {":/edit3d/images/particles_off.png", Utils::Theme::IconsBaseColor}});
const Utils::Icon EDIT3D_PARTICLE_PLAY({
        {":/edit3d/images/particles_play.png", Utils::Theme::QmlDesigner_HighlightColor}});
const Utils::Icon EDIT3D_PARTICLE_PAUSE({
        {":/edit3d/images/particles_pause.png", Utils::Theme::QmlDesigner_HighlightColor}});
const Utils::Icon EDIT3D_PARTICLE_RESTART({
        {":/edit3d/images/particles_restart.png", Utils::Theme::QmlDesigner_HighlightColor}});
const Utils::Icon EDIT3D_SELECTION_MODE_ON({
        {":/edit3d/images/select_group.png", Utils::Theme::QmlDesigner_HighlightColor}});
const Utils::Icon EDIT3D_SELECTION_MODE_OFF({
        {":/edit3d/images/select_item.png", Utils::Theme::IconsBaseColor}});
const Utils::Icon EDIT3D_MOVE_TOOL_ON({
        {":/edit3d/images/move_on.png", Utils::Theme::QmlDesigner_HighlightColor}});
const Utils::Icon EDIT3D_MOVE_TOOL_OFF({
        {":/edit3d/images/move_off.png", Utils::Theme::IconsBaseColor}});
const Utils::Icon EDIT3D_ROTATE_TOOL_ON({
        {":/edit3d/images/rotate_on.png", Utils::Theme::QmlDesigner_HighlightColor}});
const Utils::Icon EDIT3D_ROTATE_TOOL_OFF({
        {":/edit3d/images/rotate_off.png", Utils::Theme::IconsBaseColor}});
const Utils::Icon EDIT3D_SCALE_TOOL_ON({
        {":/edit3d/images/scale_on.png", Utils::Theme::QmlDesigner_HighlightColor}});
const Utils::Icon EDIT3D_SCALE_TOOL_OFF({
        {":/edit3d/images/scale_off.png", Utils::Theme::IconsBaseColor}});
const Utils::Icon EDIT3D_FIT_SELECTED_OFF({
        {":/edit3d/images/fit_selected.png", Utils::Theme::IconsBaseColor}});
const Utils::Icon EDIT3D_EDIT_CAMERA_ON({
        {":/edit3d/images/perspective_camera.png", Utils::Theme::QmlDesigner_HighlightColor}});
const Utils::Icon EDIT3D_EDIT_CAMERA_OFF({
        {":/edit3d/images/orthographic_camera.png", Utils::Theme::IconsBaseColor}});
const Utils::Icon EDIT3D_ORIENTATION_ON({
        {":/edit3d/images/global.png", Utils::Theme::QmlDesigner_HighlightColor}});
const Utils::Icon EDIT3D_ORIENTATION_OFF({
        {":/edit3d/images/local.png", Utils::Theme::IconsBaseColor}});
const Utils::Icon EDIT3D_ALIGN_CAMERA_ON({
        {":/edit3d/images/align_camera_on.png", Utils::Theme::IconsBaseColor}});
const Utils::Icon EDIT3D_ALIGN_VIEW_ON({
        {":/edit3d/images/align_view_on.png", Utils::Theme::IconsBaseColor}});
const Utils::Icon COLOR_PALETTE({
        {":/edit3d/images/color_palette.png", Utils::Theme::IconsBaseColor}});

} // Icons
} // QmlDesigner
