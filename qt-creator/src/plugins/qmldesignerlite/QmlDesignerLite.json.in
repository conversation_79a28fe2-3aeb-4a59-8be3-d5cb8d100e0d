{
    "Name" : "QmlDesignerLite",
    "Version" : "${IDE_VERSION}",
    "CompatVersion" : "${IDE_VERSION_COMPAT}",
    "Vendor" : "The Qt Company Ltd",
    "Copyright" : "(C) ${IDE_COPYRIGHT_YEAR} The Qt Company Ltd",
    "License" : [ "Commercial Usage",
                  "",
                  "Licensees holding valid Qt Commercial licenses may use this plugin in accordance with the Qt Commercial License Agreement provided with the Software or, alternatively, in accordance with the terms contained in a written agreement between you and The Qt Company.",
                  "",
                  "GNU General Public License Usage",
                  "",
                  "Alternatively, this plugin may be used under the terms of the GNU General Public License version 3 as published by the Free Software Foundation with exceptions as appearing in the file LICENSE.GPL3-EXCEPT included in the packaging of this plugin. Please review the following information to ensure the GNU General Public License requirements will be met: https://www.gnu.org/licenses/gpl-3.0.html."
    ],
    "Category" : "Qt Quick",
    "Description" : "Qml Designer Lite.",
    "LongDescription": "Qml Designer Lite is a lightweight version of Qt Design Studio, providing a subset of the features of the full Qt Design Studio.",
    "Url" : "https://www.qt.io",
    "Experimental": true,
    ${IDE_PLUGIN_DEPENDENCIES}
}
