{
    "Name" : "Qnx",
    "Version" : "${IDE_VERSION}",
    "CompatVersion" : "${IDE_VERSION_COMPAT}",
    "Vendor" : "BlackBerry",
    "Copyright" : "(C) 2017 BlackBerry, (C) ${IDE_COPYRIGHT_YEAR} The Qt Company Ltd",
    "License" : [ "Commercial Usage",
                  "",
                  "Licensees holding valid Qt Commercial licenses may use this plugin in accordance with the Qt Commercial License Agreement provided with the Software or, alternatively, in accordance with the terms contained in a written agreement between you and The Qt Company.",
                  "",
                  "GNU General Public License Usage",
                  "",
                  "Alternatively, this plugin may be used under the terms of the GNU General Public License version 3 as published by the Free Software Foundation with exceptions as appearing in the file LICENSE.GPL3-EXCEPT included in the packaging of this plugin. Please review the following information to ensure the GNU General Public License requirements will be met: https://www.gnu.org/licenses/gpl-3.0.html."
    ],
    "Category" : "Device Support",
    "Description" : "Develop for QNX Neutrino devices.",
    "LongDescription" : [
        "Connect devices with USB or over a network to run, debug, and analyze applications built for them.",
        "You also need:",
            "- Qt for QNX",
            "- QNX Software Development Platform (SDP)"
    ],
    "Url" : "https://www.blackberry.com",
    ${IDE_PLUGIN_DEPENDENCIES}
}
