# This is the CMakeCache file.
# For build in directory: /home/<USER>/Program/openmv-ide/build
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Build and install design studio plugins, examples and settings.
BUILD_DESIGNSTUDIO:BOOL=OFF

//Build documentation by default
BUILD_DOCS_BY_DEFAULT:BOOL=OFF

//Build executables by default. This can be used to build all executables
// by default, or none.
BUILD_EXECUTABLES_BY_DEFAULT:BOOL=ON

//Build libraries by default. This can be used to build all libraries
// by default, or none.
BUILD_LIBRARIES_BY_DEFAULT:BOOL=ON

//Build plugins by default. This can be used to build all plugins
// by default, or none.
BUILD_PLUGINS_BY_DEFAULT:BOOL=ON

//Build tests by default. This can be used to build all tests by
// default, or none.
BUILD_TESTS_BY_DEFAULT:BOOL=ON

//Build with precompiled headers
BUILD_WITH_PCH:BOOL=ON

//No help, variable specified on the command line.
CMAKE_BUILD_TYPE:UNINITIALIZED=Release

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/home/<USER>/Program/openmv-ide/build/CMakeFiles/pkgRedirects

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Program used to build from build.ninja files.
CMAKE_MAKE_PROGRAM:FILEPATH=CMAKE_MAKE_PROGRAM-NOTFOUND

//No help, variable specified on the command line.
CMAKE_PREFIX_PATH:UNINITIALIZED=/usr/lib/qt6

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=QtCreator

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=4.7.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=4

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=7

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Marks the presence of IDE revision string.
IDE_REVISION:BOOL=FALSE

//The IDE revision string.
IDE_REVISION_STR:STRING=

//The IDE revision Url string.
IDE_REVISION_URL:STRING=

//Extract debug information from binary files.
QTC_SEPARATE_DEBUG_INFO:BOOL=OFF

//Builds libraries and plugins as static libraries
QTC_STATIC_BUILD:BOOL=OFF

//Uses llvm-link linker if found.
QTC_USE_LLVM_LINKER:BOOL=OFF

//Value Computed by CMake
QtCreator_BINARY_DIR:STATIC=/home/<USER>/Program/openmv-ide/build

//Value Computed by CMake
QtCreator_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
QtCreator_SOURCE_DIR:STATIC=/home/<USER>/Program/openmv-ide/qt-creator

//Enables support for building with CCACHE and separate debug info
// with MSVC, which CCACHE normally doesn't support.
WITH_CCACHE_SUPPORT:BOOL=OFF

//Build documentation
WITH_DOCS:BOOL=OFF

//Build online documentation
WITH_ONLINE_DOCS:BOOL=OFF

//Enables support for building with SCCACHE and separate debug
// info with MSVC, which SCCACHE normally doesn't support.
WITH_SCCACHE_SUPPORT:BOOL=OFF


########################
# INTERNAL cache entries
########################

//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/Program/openmv-ide/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=0
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=3
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/usr/bin/cmake-gui
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/Program/openmv-ide/qt-creator
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: IDE_REVISION
IDE_REVISION-ADVANCED:INTERNAL=1
//ADVANCED property for variable: IDE_REVISION_STR
IDE_REVISION_STR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: IDE_REVISION_URL
IDE_REVISION_URL-ADVANCED:INTERNAL=1
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=
//*** Internal ***
__QTC_EXECUTABLES:INTERNAL=
//*** Internal ***
__QTC_LIBRARIES:INTERNAL=
//*** Internal ***
__QTC_PLUGINS:INTERNAL=
//*** Internal ***
__QTC_RESOURCE_FILES:INTERNAL=
//*** Internal ***
__QTC_TESTS:INTERNAL=

